import { useState, useEffect } from 'react';

export interface BlogImage {
  id: string;
  name: string;
  objectKey: string;
  mimeType: string;
  fileSize: number;
  width?: number;
  height?: number;
  url: string;
  createdAt: string;
  updatedAt: string;
}

export function useBlogImages() {
  const [images, setImages] = useState<BlogImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchImages = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/images?type=BLOG_IMAGE');
      if (!response.ok) {
        throw new Error('Failed to fetch blog images');
      }

      const data = await response.json();
      setImages(data.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const uploadImage = async (
    file: File,
    name: string,
    publicName: string,
    resize?: { width: number; height: number }
  ): Promise<boolean> => {
    try {
      setError(null);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', name);
      formData.append('publicName', publicName);
      formData.append('type', 'BLOG_IMAGE');

      // Add resize parameters if provided
      if (resize) {
        formData.append('resize', JSON.stringify(resize));
      }

      const response = await fetch('/api/admin/images', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      }

      const newImage: BlogImage = await response.json();
      setImages((prev) => [newImage, ...prev]);
      return true;
    } catch (err) {
      setError(`Upload failed: ${err instanceof Error ? err.message : String(err)}`);
      return false;
    }
  };

  const deleteImage = async (imageId: string): Promise<void> => {
    try {
      setError(null);
      
      const response = await fetch(`/api/admin/images/${imageId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete image');
      }

      setImages((prev) => prev.filter((img) => img.id !== imageId));
    } catch (err) {
      setError(`Delete failed: ${err instanceof Error ? err.message : String(err)}`);
      throw err;
    }
  };

  useEffect(() => {
    fetchImages();
  }, []);

  return {
    images,
    loading,
    error,
    fetchImages,
    uploadImage,
    deleteImage,
  };
}
