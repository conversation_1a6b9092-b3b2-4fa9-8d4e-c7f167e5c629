import React, { useState, ChangeEvent, useEffect, DragEvent } from 'react';
import {
  ArrowUpTrayIcon,
  XMarkIcon,
  PhotoIcon,
  ArrowPathIcon,
  SunIcon,
  MoonIcon,
  ExclamationCircleIcon,
  ScissorsIcon,
} from '@heroicons/react/24/outline';
import { PublicImageType } from '@prisma/client';
import { useImages } from '../hooks/useImages';
import { showErrorDialog } from '@/shared/utils/dialogs';
import { ImageUploadOptions } from '../types/imageTypes';
import CropModal from './CropModal';

interface ImageUploadFormProps {
  imageType: PublicImageType;
  onUpload: (options: ImageUploadOptions) => Promise<boolean>;
  maxResolution?: number; // Max resolution for the slider (400 for icons, 2000 for blog images)
}

export default function ImageUploadForm({ imageType, onUpload, maxResolution = 400 }: ImageUploadFormProps) {
  const { images } = useImages(imageType);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [name, setName] = useState('');
  const [publicName, setPublicName] = useState('');
  const [nameError, setNameError] = useState<string | null>(null);
  const [publicNameError, setPublicNameError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewSize, setPreviewSize] = useState(100);
  const [imageNaturalSize, setImageNaturalSize] = useState({ width: 0, height: 0 });
  const [enableResize, setEnableResize] = useState(false);
  const [fileExtension, setFileExtension] = useState('');
  const [darkBackground, setDarkBackground] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [showCropModal, setShowCropModal] = useState(false);
  const [cropData, setCropData] = useState<{ left: number; top: number; width: number; height: number } | null>(null);
  const [cropResizeData, setCropResizeData] = useState<{ width: number; height: number } | null>(null);

  // Clean up the object URL when component unmounts or file changes
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  function onFileChange(e: ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0] ?? null;
    if (!file) return;

    // Validate file type
    if (!['image/png', 'image/jpeg', 'image/jpg', 'image/webp'].includes(file.type)) {
      showErrorDialog('Nur PNG, JPG und WebP Dateien sind erlaubt.');
      return;
    }

    // Create preview URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(URL.createObjectURL(file));

    // Extract extension
    const extension = file.name.match(/\.([^.]+)$/)?.[0] || '';
    setFileExtension(extension);

    setSelectedFile(file);
    setName(file.name.replace(/\..+$/, '')); // strip extension
    setPublicName(file.name.replace(/\..+$/, '')); // strip extension for public name too
  }

  function handleImageLoad(e: React.SyntheticEvent<HTMLImageElement>) {
    const img = e.currentTarget;
    const naturalWidth = img.naturalWidth;
    const naturalHeight = img.naturalHeight;

    setImageNaturalSize({
      width: naturalWidth,
      height: naturalHeight,
    });

    // Set initial preview size to the maximum dimension of the image (capped at maxResolution)
    const maxDimension = Math.max(naturalWidth, naturalHeight);
    setPreviewSize(Math.min(maxResolution, maxDimension));
  }

  // Check for name collisions when name or images change
  useEffect(() => {
    if (name && images.length > 0) {
      const nameExists = images.some((image) => image.name.toLowerCase() === name.toLowerCase());
      setNameError(nameExists ? `Ein ${getImageTypeLabel()} mit diesem Namen existiert bereits` : null);
    } else {
      setNameError(null);
    }
  }, [name, images]);

  // Check for public name collisions
  useEffect(() => {
    if (publicName && images.length > 0) {
      const publicNameWithExt = publicName + fileExtension;
      const prefix = imageType === PublicImageType.EMAIL_ICON ? 'email' :
                    imageType === PublicImageType.BLOG_IMAGE ? 'blog' : 'images';
      const keyExists = images.some(
        (image) => image.objectKey.toLowerCase() === `${prefix}/${publicNameWithExt}`.toLowerCase()
      );
      setPublicNameError(keyExists ? `Ein ${getImageTypeLabel()} mit diesem Namen existiert bereits` : null);
    } else {
      setPublicNameError(null);
    }
  }, [publicName, fileExtension, images, imageType]);

  // Add these new handlers for drag and drop
  function handleDragOver(e: DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }

  function handleDragEnter(e: DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }

  function handleDragLeave(e: DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }

  function handleDrop(e: DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0]; // Take only the first file

      // Validate file type
      if (!['image/png', 'image/jpeg', 'image/jpg', 'image/webp'].includes(file.type)) {
        showErrorDialog('Nur PNG, JPG und WebP Dateien sind erlaubt.');
        return;
      }

      // Create preview URL
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
      setPreviewUrl(URL.createObjectURL(file));

      // Extract extension
      const extension = file.name.match(/\.([^.]+)$/)?.[0] || '';
      setFileExtension(extension);

      setSelectedFile(file);
      setName(file.name.replace(/\..+$/, '')); // strip extension
      setPublicName(file.name.replace(/\..+$/, '')); // strip extension for public name too
    }
  }

  // Calculate display dimensions while maintaining aspect ratio
  const getDisplayDimensions = () => {
    if (imageNaturalSize.width === 0 || imageNaturalSize.height === 0) {
      return { width: previewSize, height: previewSize };
    }

    // If resize is disabled, return original dimensions if they're below maxResolution
    if (!enableResize) {
      if (imageNaturalSize.width <= maxResolution && imageNaturalSize.height <= maxResolution) {
        return { width: imageNaturalSize.width, height: imageNaturalSize.height };
      }
      // Otherwise, scale down to fit within maxResolution
      const aspectRatio = imageNaturalSize.width / imageNaturalSize.height;
      if (aspectRatio > 1) {
        return {
          width: maxResolution,
          height: Math.round(maxResolution / aspectRatio),
        };
      } else {
        return {
          width: Math.round(maxResolution * aspectRatio),
          height: maxResolution,
        };
      }
    }

    const aspectRatio = imageNaturalSize.width / imageNaturalSize.height;

    if (aspectRatio > 1) {
      // Wider than tall
      return {
        width: previewSize,
        height: Math.round(previewSize / aspectRatio),
      };
    } else {
      // Taller than wide
      return {
        width: Math.round(previewSize * aspectRatio),
        height: previewSize,
      };
    }
  };

  const displayDimensions = getDisplayDimensions();

  function handleSizeInputChange(e: ChangeEvent<HTMLInputElement>) {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      setPreviewSize(Math.min(maxResolution, value));
    }
  }

  // Add crop handlers
  function handleCropClick() {
    setShowCropModal(true);
  }

  function handleCropComplete(
    newCropData: { left: number; top: number; width: number; height: number },
    resizeData?: { width: number; height: number }
  ) {
    setCropData(newCropData);
    setCropResizeData(resizeData || null);
    // If crop provides resize data, disable manual resize
    if (resizeData) {
      setEnableResize(false);
    }
  }

  function handleClearCrop() {
    setCropData(null);
    setCropResizeData(null);
  }

  // Add a resetForm function
  function resetForm() {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setSelectedFile(null);
    setName('');
    setPublicName('');
    setPreviewUrl(null);
    setFileExtension('');
    setEnableResize(false);
    setCropData(null);
    setCropResizeData(null);
  }

  function toggleBackground() {
    setDarkBackground((prev) => !prev);
  }

  async function handleUpload() {
    if (!selectedFile) return;
    if (nameError || publicNameError) return;

    setUploading(true);

    // Add extension back to publicName
    const finalPublicName = publicName + fileExtension;

    // Pass resize dimensions - prioritize crop resize data over manual resize
    const resizeOptions = cropResizeData || (enableResize
      ? {
          width: displayDimensions.width,
          height: displayDimensions.height,
        }
      : undefined);

    const success = await onUpload({
      file: selectedFile,
      name,
      publicName: finalPublicName,
      resize: resizeOptions,
      crop: cropData || undefined,
    });

    if (success) {
      // Reset form
      resetForm();
    }

    setUploading(false);
  }

  const getImageTypeLabel = () => {
    switch (imageType) {
      case PublicImageType.EMAIL_ICON:
        return 'Icon';
      case PublicImageType.BLOG_IMAGE:
        return 'Blog-Bild';
      default:
        return 'Bild';
    }
  };

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
      <h2 className="text-xl font-semibold mb-4 flex items-center">
        <PhotoIcon className="h-6 w-6 mr-2 text-blue-600" />
        {getImageTypeLabel()} hochladen
      </h2>

      {!selectedFile ? (
        <div
          className={`border-2 border-dashed ${
            isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50'
          } p-8 rounded-lg text-center cursor-pointer transition-colors mb-6 group`}
          onClick={() => document.getElementById('file-input')?.click()}
          onDragOver={handleDragOver}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <ArrowUpTrayIcon
            className={`h-10 w-10 mx-auto mb-3 ${
              isDragging ? 'text-blue-500' : 'text-gray-400 group-hover:text-blue-500'
            } transition-colors`}
          />
          <div
            className={`${isDragging ? 'text-gray-700' : 'text-gray-500 group-hover:text-gray-700'} transition-colors`}
          >
            Klicken oder Datei hier ablegen
          </div>
          <p className="text-xs text-gray-400 mt-2">PNG, JPG, WebP (max. 5MB)</p>
          <input
            id="file-input"
            type="file"
            accept="image/png,image/jpeg,image/jpg,image/webp"
            className="hidden"
            onChange={onFileChange}
          />
        </div>
      ) : null}

      {selectedFile && (
        <div className="flex flex-col md:flex-row gap-6">
          {/* Form inputs - left side */}
          <div className="flex-1 space-y-4">

            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700">Name</label>
              <div className="relative">
                <input
                  type="text"
                  className={`w-full border ${nameError ? 'border-red-500' : 'border-gray-300'} rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all`}
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                />
                {nameError && (
                  <div className="flex items-center mt-1 text-red-500 text-sm">
                    <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                    {nameError}
                  </div>
                )}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700">
                Öffentlicher Name (Objektschlüssel)
              </label>
              <div className="relative">
                <input
                  type="text"
                  className={`w-full border ${publicNameError ? 'border-red-500' : 'border-gray-300'} rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all`}
                  value={publicName}
                  onChange={(e) => setPublicName(e.target.value)}
                />
                {publicNameError && (
                  <div className="flex items-center mt-1 text-red-500 text-sm">
                    <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                    {publicNameError}
                  </div>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Öffentlicher Name: {publicName.replace(/\s+/g, '-')}
                  {fileExtension}
                </p>
              </div>
            </div>

            {previewUrl && (
              <div className="space-y-3 pt-2 pb-1">
                <div className="flex items-center mb-2">
                  <input
                    type="checkbox"
                    id="enable-resize"
                    checked={enableResize}
                    onChange={(e) => setEnableResize(e.target.checked)}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 rounded"
                  />
                  <label htmlFor="enable-resize" className="text-sm font-medium text-gray-700">
                    Bildgröße ändern
                  </label>
                </div>

                <div className="flex justify-between text-sm text-gray-600">
                  <div>
                    <span className="font-medium">Original:</span> {imageNaturalSize.width} × {imageNaturalSize.height}{' '}
                    px
                  </div>
                  {enableResize && (
                    <div>
                      <span className="font-medium">Neue Größe:</span> {displayDimensions.width} ×{' '}
                      {displayDimensions.height} px
                    </div>
                  )}
                </div>

                {enableResize && (
                  <div className="flex items-center gap-3">
                    <div className="flex-1">
                      <label className="block text-xs text-gray-500 mb-1">Größe:</label>
                      <input
                        type="range"
                        min="32"
                        max={Math.min(maxResolution, Math.max(imageNaturalSize.width, imageNaturalSize.height))}
                        value={previewSize}
                        onChange={(e) => setPreviewSize(parseInt(e.target.value))}
                        className="w-full accent-blue-600"
                      />
                    </div>
                    <div className="w-20 flex items-center">
                      <input
                        type="number"
                        min="32"
                        max={Math.min(maxResolution, Math.max(imageNaturalSize.width, imageNaturalSize.height))}
                        value={previewSize}
                        onChange={handleSizeInputChange}
                        className="w-full border border-gray-300 rounded-lg px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      />
                    </div>
                  </div>
                )}

                {/* Crop section - only for blog images */}
                {imageType === PublicImageType.BLOG_IMAGE && (
                  <div className="space-y-3 pt-2 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={handleCropClick}
                      className="w-full px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                    >
                      <ScissorsIcon className="h-4 w-4 mr-2" />
                      Für Titelbild zuschneiden
                    </button>

                    {cropData && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div className="text-sm text-blue-800">
                            <div className="font-medium">Zuschnitt aktiv:</div>
                            <div className="text-xs">
                              Bereich: {cropData.width} × {cropData.height} px (Position: {cropData.left}, {cropData.top})
                            </div>
                            {cropResizeData && (
                              <div className="text-xs mt-1">
                                Automatische Größenanpassung: {cropResizeData.width} × {cropResizeData.height} px
                              </div>
                            )}
                          </div>
                          <button
                            type="button"
                            onClick={handleClearCrop}
                            className="text-xs text-blue-600 hover:text-blue-800 underline"
                          >
                            Entfernen
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            <div className="flex gap-3 pt-2">
              <button
                onClick={resetForm}
                type="button"
                className="flex-1 px-4 py-2 rounded-lg text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 transition-colors flex items-center justify-center"
              >
                <XMarkIcon className="h-5 w-5 mr-2" />
                Abbrechen
              </button>
              <button
                onClick={handleUpload}
                disabled={uploading || nameError !== null || publicNameError !== null || !selectedFile}
                className={`flex-1 px-4 py-2 rounded-lg text-white flex items-center justify-center transition-colors ${
                  uploading || nameError !== null || publicNameError !== null || !selectedFile
                    ? 'bg-blue-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {uploading ? (
                  <>
                    <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
                    Wird hochgeladen...
                  </>
                ) : (
                  <>
                    <ArrowUpTrayIcon className="h-5 w-5 mr-2" />
                    {getImageTypeLabel()} hochladen
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Preview - right side */}
          {previewUrl && (
            <div className="flex-1">
              <div className="relative">
                <div
                  className={`relative border border-gray-200 rounded-lg shadow-sm overflow-hidden transition-colors duration-300 ${
                    darkBackground ? 'bg-gray-800' : 'bg-white'
                  }`}
                  style={{ width: '400px', height: '400px' }}
                >
                  <div
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    <div className="relative">
                      <img
                        src={previewUrl}
                        alt={`${getImageTypeLabel()} Vorschau`}
                        style={{
                          width: `${displayDimensions.width}px`,
                          height: `${displayDimensions.height}px`,
                        }}
                        className="block"
                        onLoad={handleImageLoad}
                      />

                      {/* Show crop rectangle overlay if crop data exists */}
                      {cropData && (
                        <div
                          className="absolute border-2 border-blue-500 pointer-events-none"
                          style={{
                            left: `${(cropData.left / imageNaturalSize.width) * displayDimensions.width}px`,
                            top: `${(cropData.top / imageNaturalSize.height) * displayDimensions.height}px`,
                            width: `${(cropData.width / imageNaturalSize.width) * displayDimensions.width}px`,
                            height: `${(cropData.height / imageNaturalSize.height) * displayDimensions.height}px`,
                          }}
                        />
                      )}
                    </div>
                  </div>
                </div>

                {/* Toggle switch in top-right corner */}
                <div className="absolute top-2 right-6">
                  <button
                    onClick={toggleBackground}
                    type="button"
                    className="p-1 rounded-full bg-white border border-gray-200 shadow-sm hover:bg-gray-50 transition-colors"
                    aria-label={darkBackground ? 'Heller Hintergrund' : 'Dunkler Hintergrund'}
                  >
                    {darkBackground ? (
                      <SunIcon className="h-5 w-5 text-orange-500" />
                    ) : (
                      <MoonIcon className="h-5 w-5 text-gray-700" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Crop Modal */}
      {previewUrl && (
        <CropModal
          isOpen={showCropModal}
          onClose={() => setShowCropModal(false)}
          imageUrl={previewUrl}
          onCrop={handleCropComplete}
          aspectRatio={1200 / 630} // OG image aspect ratio
        />
      )}
    </div>
  );
}
