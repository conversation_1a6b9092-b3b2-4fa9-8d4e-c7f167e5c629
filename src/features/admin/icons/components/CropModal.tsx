import React, { useState, useRef, useEffect } from 'react';
import { XMarkIcon, ScissorsIcon, CheckIcon } from '@heroicons/react/24/outline';

interface CropModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  onCrop: (
    cropData: { left: number; top: number; width: number; height: number },
    resizeData?: { width: number; height: number }
  ) => void;
  aspectRatio?: number; // width/height ratio, default 1200/630 for OG images
}

export default function CropModal({ 
  isOpen, 
  onClose, 
  imageUrl, 
  onCrop, 
  aspectRatio = 1200 / 630 
}: CropModalProps) {
  const [cropRect, setCropRect] = useState({ x: 50, y: 50, width: 200, height: 200 / aspectRatio });
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [displaySize, setDisplaySize] = useState({ width: 0, height: 0 });
  const [imageLoaded, setImageLoaded] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Reset crop when modal opens
  useEffect(() => {
    if (isOpen) {
      setCropRect({ x: 50, y: 50, width: 200, height: 200 / aspectRatio });
      setImageLoaded(false);
    }
  }, [isOpen, aspectRatio]);

  const handleImageLoad = () => {
    if (imageRef.current && containerRef.current) {
      const img = imageRef.current;

      // Get natural image dimensions
      const naturalWidth = img.naturalWidth;
      const naturalHeight = img.naturalHeight;
      setImageSize({ width: naturalWidth, height: naturalHeight });

      // Calculate display dimensions (fit within container while maintaining aspect ratio)
      const maxDisplayWidth = 800;
      const maxDisplayHeight = 600;

      let displayWidth = naturalWidth;
      let displayHeight = naturalHeight;

      // Scale down if needed
      if (displayWidth > maxDisplayWidth || displayHeight > maxDisplayHeight) {
        const scaleX = maxDisplayWidth / displayWidth;
        const scaleY = maxDisplayHeight / displayHeight;
        const scale = Math.min(scaleX, scaleY);

        displayWidth = displayWidth * scale;
        displayHeight = displayHeight * scale;
      }

      setDisplaySize({ width: displayWidth, height: displayHeight });

      // Calculate the largest possible crop rectangle with the target aspect ratio
      // that fits within the image
      const targetAspectRatio = aspectRatio;
      const imageAspectRatio = naturalWidth / naturalHeight;

      let cropWidth, cropHeight;

      if (imageAspectRatio > targetAspectRatio) {
        // Image is wider than target ratio - height is the limiting factor
        cropHeight = displayHeight;
        cropWidth = cropHeight * targetAspectRatio;
      } else {
        // Image is taller than target ratio - width is the limiting factor
        cropWidth = displayWidth;
        cropHeight = cropWidth / targetAspectRatio;
      }

      // Center the crop rectangle
      const cropX = (displayWidth - cropWidth) / 2;
      const cropY = (displayHeight - cropHeight) / 2;

      setCropRect({
        x: Math.max(0, cropX),
        y: Math.max(0, cropY),
        width: cropWidth,
        height: cropHeight
      });

      setImageLoaded(true);
    }
  };

  const handleMouseDown = (e: React.MouseEvent, action: 'drag' | 'resize') => {
    e.preventDefault();
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    setDragStart({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });

    if (action === 'drag') {
      setIsDragging(true);
    } else {
      setIsResizing(true);
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging && !isResizing) return;
    if (!containerRef.current || !imageRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    if (isDragging) {
      const deltaX = mouseX - dragStart.x;
      const deltaY = mouseY - dragStart.y;
      
      setCropRect(prev => {
        const newX = Math.max(0, Math.min(displaySize.width - prev.width, prev.x + deltaX));
        const newY = Math.max(0, Math.min(displaySize.height - prev.height, prev.y + deltaY));
        
        return { ...prev, x: newX, y: newY };
      });

      setDragStart({ x: mouseX, y: mouseY });
    } else if (isResizing) {
      const deltaX = mouseX - dragStart.x;
      
      setCropRect(prev => {
        const newWidth = Math.max(50, Math.min(displaySize.width - prev.x, prev.width + deltaX));
        const newHeight = newWidth / aspectRatio;
        
        // Ensure height doesn't exceed bounds
        const maxHeight = displaySize.height - prev.y;
        const finalHeight = Math.min(newHeight, maxHeight);
        const finalWidth = finalHeight * aspectRatio;
        
        return { ...prev, width: finalWidth, height: finalHeight };
      });

      setDragStart({ x: mouseX, y: mouseY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setIsResizing(false);
  };

  const handleCrop = () => {
    if (!imageRef.current || displaySize.width === 0 || displaySize.height === 0) return;

    // Convert display coordinates to actual image coordinates
    const scaleX = imageSize.width / displaySize.width;
    const scaleY = imageSize.height / displaySize.height;

    const actualCropWidth = Math.round(cropRect.width * scaleX);
    const actualCropHeight = Math.round(cropRect.height * scaleY);

    const cropData = {
      left: Math.round(cropRect.x * scaleX),
      top: Math.round(cropRect.y * scaleY),
      width: actualCropWidth,
      height: actualCropHeight
    };

    // Calculate resize dimensions (scale down only to 1200x630)
    let resizeData = undefined;
    if (actualCropWidth > 1200 || actualCropHeight > 630) {
      const scaleToFit = Math.min(1200 / actualCropWidth, 630 / actualCropHeight);
      resizeData = {
        width: Math.round(actualCropWidth * scaleToFit),
        height: Math.round(actualCropHeight * scaleToFit)
      };
    }

    onCrop(cropData, resizeData);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <ScissorsIcon className="h-6 w-6 mr-2 text-blue-600" />
            <h2 className="text-xl font-semibold">Bild zuschneiden</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              Ziehen Sie das Rechteck, um den gewünschten Bereich auszuwählen. 
              Ziehen Sie an der unteren rechten Ecke, um die Größe zu ändern.
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Seitenverhältnis: {aspectRatio.toFixed(2)}:1 (OG-Bilder: 1200×630px)
            </p>
          </div>

          {/* Image container */}
          <div className="flex justify-center mb-6">
            <div
              ref={containerRef}
              className="relative border border-gray-300 rounded-lg overflow-hidden bg-gray-100"
              style={{
                width: displaySize.width > 0 ? `${displaySize.width}px` : '800px',
                height: displaySize.height > 0 ? `${displaySize.height}px` : '600px',
                maxWidth: '100%',
                maxHeight: '60vh'
              }}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            >
              <img
                ref={imageRef}
                src={imageUrl}
                alt="Zu zuschneidendes Bild"
                className="block w-full h-full object-contain"
                onLoad={handleImageLoad}
                draggable={false}
              />

              {/* Loading indicator */}
              {!imageLoaded && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                  <div className="text-gray-500">Bild wird geladen...</div>
                </div>
              )}

              {/* Crop overlay */}
              {imageLoaded && displaySize.width > 0 && (
                <>
                  {/* Dark overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-40 pointer-events-none" />
                  
                  {/* Crop rectangle */}
                  <div
                    className="absolute border-2 border-blue-500 bg-transparent cursor-move"
                    style={{
                      left: `${cropRect.x}px`,
                      top: `${cropRect.y}px`,
                      width: `${cropRect.width}px`,
                      height: `${cropRect.height}px`,
                    }}
                    onMouseDown={(e) => handleMouseDown(e, 'drag')}
                  >
                    {/* Clear area inside crop rectangle */}
                    <div className="absolute inset-0 bg-white bg-opacity-20" />
                    
                    {/* Resize handle */}
                    <div
                      className="absolute bottom-0 right-0 w-4 h-4 bg-blue-500 cursor-se-resize border border-white"
                      style={{ transform: 'translate(50%, 50%)' }}
                      onMouseDown={(e) => {
                        e.stopPropagation();
                        handleMouseDown(e, 'resize');
                      }}
                    />
                    
                    {/* Corner indicators */}
                    <div className="absolute top-0 left-0 w-2 h-2 bg-blue-500 border border-white" style={{ transform: 'translate(-50%, -50%)' }} />
                    <div className="absolute top-0 right-0 w-2 h-2 bg-blue-500 border border-white" style={{ transform: 'translate(50%, -50%)' }} />
                    <div className="absolute bottom-0 left-0 w-2 h-2 bg-blue-500 border border-white" style={{ transform: 'translate(-50%, 50%)' }} />
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Crop info */}
          {imageLoaded && displaySize.width > 0 && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Ausgewählter Bereich:</span>
                  <div className="text-gray-600">
                    {Math.round(cropRect.width * (imageSize.width / displaySize.width))} × {Math.round(cropRect.height * (imageSize.height / displaySize.height))} px
                  </div>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Position:</span>
                  <div className="text-gray-600">
                    X: {Math.round(cropRect.x * (imageSize.width / displaySize.width))},
                    Y: {Math.round(cropRect.y * (imageSize.height / displaySize.height))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Abbrechen
            </button>
            <button
              onClick={handleCrop}
              disabled={!imageLoaded}
              className={`flex-1 px-4 py-2 rounded-lg transition-colors flex items-center justify-center ${
                imageLoaded
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              <CheckIcon className="h-5 w-5 mr-2" />
              Zuschnitt übernehmen
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
