import React, { useState, ChangeEvent, useEffect, DragEvent } from 'react';
import {
  ArrowUpTrayIcon,
  XMarkIcon,
  PhotoIcon,
  ArrowPathIcon,
  SunIcon,
  MoonIcon,
  ExclamationCircleIcon,
} from '@heroicons/react/24/outline';
import { useIcons } from '../hooks/useIcons';
import { showErrorDialog } from '@/shared/utils/dialogs';

interface IconUploadFormProps {
  onUpload: (
    file: File,
    name: string,
    publicName: string,
    resize?: { width: number; height: number }
  ) => Promise<boolean>;
}

export default function IconUploadForm({ onUpload }: IconUploadFormProps) {
  const { icons } = useIcons();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [name, setName] = useState('');
  const [publicName, setPublicName] = useState('');
  const [nameError, setNameError] = useState<string | null>(null);
  const [publicNameError, setPublicNameError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewSize, setPreviewSize] = useState(100); // Default size in pixels
  const [imageNaturalSize, setImageNaturalSize] = useState({ width: 0, height: 0 });
  const [enableResize, setEnableResize] = useState(false);
  const [fileExtension, setFileExtension] = useState('');
  const [darkBackground, setDarkBackground] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  // Clean up the object URL when component unmounts or file changes
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  function onFileChange(e: ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0] ?? null;
    if (!file) return;

    // Validate file type
    if (!['image/png', 'image/jpeg', 'image/jpg', 'image/webp'].includes(file.type)) {
      showErrorDialog('Nur PNG, JPG und WebP Dateien sind erlaubt.');
      return;
    }

    // Create preview URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(URL.createObjectURL(file));

    // Extract extension
    const extension = file.name.match(/\.([^.]+)$/)?.[0] || '';
    setFileExtension(extension);

    setSelectedFile(file);
    setName(file.name.replace(/\..+$/, '')); // strip extension
    setPublicName(file.name.replace(/\..+$/, '')); // strip extension for public name too
  }

  function handleImageLoad(e: React.SyntheticEvent<HTMLImageElement>) {
    const img = e.currentTarget;
    const naturalWidth = img.naturalWidth;
    const naturalHeight = img.naturalHeight;

    console.log('Image loaded with dimensions:', naturalWidth, 'x', naturalHeight);

    setImageNaturalSize({
      width: naturalWidth,
      height: naturalHeight,
    });

    // Always set initial preview size to the maximum dimension of the image
    // (capped at 400 if larger than 400)
    const maxDimension = Math.max(naturalWidth, naturalHeight);
    setPreviewSize(Math.min(400, maxDimension));
  }

  function handleSizeInputChange(e: ChangeEvent<HTMLInputElement>) {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      setPreviewSize(Math.min(400, value));
    }
  }

  // Check for name collisions when name or icons change
  useEffect(() => {
    if (name && icons.length > 0) {
      const nameExists = icons.some((icon) => icon.name.toLowerCase() === name.toLowerCase());
      setNameError(nameExists ? 'Ein Icon mit diesem Namen existiert bereits' : null);
    } else {
      setNameError(null);
    }
  }, [name, icons]);

  // Check for public name collisions
  useEffect(() => {
    if (publicName && icons.length > 0) {
      const publicNameWithExt = publicName + fileExtension;
      const keyExists = icons.some(
        (icon) => icon.objectKey.toLowerCase() === `email/${publicNameWithExt}`.toLowerCase()
      );
      setPublicNameError(keyExists ? 'Ein Icon mit diesem Namen existiert bereits' : null);
    } else {
      setPublicNameError(null);
    }
  }, [publicName, fileExtension, icons]);

  async function handleUpload() {
    if (!selectedFile) return;
    if (nameError || publicNameError) return;

    setUploading(true);

    // Add extension back to publicName
    const finalPublicName = publicName + fileExtension;

    // Pass resize dimensions if enabled
    const resizeOptions = enableResize
      ? {
          width: displayDimensions.width,
          height: displayDimensions.height,
        }
      : undefined;

    const success = await onUpload(selectedFile, name, finalPublicName, resizeOptions);

    if (success) {
      // Reset form
      setSelectedFile(null);
      setName('');
      setPublicName('');
      setPreviewUrl(null);
      setFileExtension('');
      setEnableResize(false);
    }

    setUploading(false);
  }

  // Add a resetForm function
  function resetForm() {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setSelectedFile(null);
    setName('');
    setPublicName('');
    setPreviewUrl(null);
    setFileExtension('');
    setEnableResize(false);
  }

  function toggleBackground() {
    setDarkBackground((prev) => !prev);
  }

  // Add these new handlers for drag and drop
  function handleDragOver(e: DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }

  function handleDragEnter(e: DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }

  function handleDragLeave(e: DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }

  function handleDrop(e: DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0]; // Take only the first file

      // Validate file type
      if (!['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)) {
        showErrorDialog('Nur PNG und JPG Dateien sind erlaubt.');
        return;
      }

      // Create preview URL
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
      setPreviewUrl(URL.createObjectURL(file));

      // Extract extension
      const extension = file.name.match(/\.([^.]+)$/)?.[0] || '';
      setFileExtension(extension);

      setSelectedFile(file);
      setName(file.name.replace(/\..+$/, '')); // strip extension
      setPublicName(file.name.replace(/\..+$/, '')); // strip extension for public name too
    }
  }

  // Calculate display dimensions while maintaining aspect ratio
  const getDisplayDimensions = () => {
    if (imageNaturalSize.width === 0 || imageNaturalSize.height === 0) {
      return { width: previewSize, height: previewSize };
    }

    // If resize is disabled, return original dimensions if they're below 400px
    if (!enableResize) {
      if (imageNaturalSize.width <= 400 && imageNaturalSize.height <= 400) {
        return { width: imageNaturalSize.width, height: imageNaturalSize.height };
      }
      // Otherwise, scale down to fit within 400px
      const aspectRatio = imageNaturalSize.width / imageNaturalSize.height;
      if (aspectRatio > 1) {
        return {
          width: 400,
          height: Math.round(400 / aspectRatio),
        };
      } else {
        return {
          width: Math.round(400 * aspectRatio),
          height: 400,
        };
      }
    }

    const aspectRatio = imageNaturalSize.width / imageNaturalSize.height;

    if (aspectRatio > 1) {
      // Wider than tall
      return {
        width: previewSize,
        height: Math.round(previewSize / aspectRatio),
      };
    } else {
      // Taller than wide
      return {
        width: Math.round(previewSize * aspectRatio),
        height: previewSize,
      };
    }
  };

  const displayDimensions = getDisplayDimensions();

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
      <h2 className="text-xl font-semibold mb-4 flex items-center">
        <PhotoIcon className="h-6 w-6 mr-2 text-blue-600" />
        Neues Icon hochladen
      </h2>

      {!selectedFile ? (
        <div
          className={`border-2 border-dashed ${
            isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50'
          } p-8 rounded-lg text-center cursor-pointer transition-colors mb-6 group`}
          onClick={() => document.getElementById('file-input')?.click()}
          onDragOver={handleDragOver}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <ArrowUpTrayIcon
            className={`h-10 w-10 mx-auto mb-3 ${
              isDragging ? 'text-blue-500' : 'text-gray-400 group-hover:text-blue-500'
            } transition-colors`}
          />
          <div
            className={`${isDragging ? 'text-gray-700' : 'text-gray-500 group-hover:text-gray-700'} transition-colors`}
          >
            Klicken oder Datei hier ablegen
          </div>
          <p className="text-xs text-gray-400 mt-2">PNG, JPG (max. 5MB)</p>
          <input
            id="file-input"
            type="file"
            accept="image/png,image/jpeg,image/jpg"
            className="hidden"
            onChange={onFileChange}
          />
        </div>
      ) : null}

      {selectedFile && (
        <div className="flex flex-col md:flex-row gap-6">
          {/* Form inputs - left side */}
          <div className="flex-1 space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700">Name</label>
              <div className="relative">
                <input
                  type="text"
                  className={`w-full border ${nameError ? 'border-red-500' : 'border-gray-300'} rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all`}
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                />
                {nameError && (
                  <div className="flex items-center mt-1 text-red-500 text-sm">
                    <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                    {nameError}
                  </div>
                )}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700">
                Öffentlicher Name (Objektschlüssel)
              </label>
              <div className="relative">
                <input
                  type="text"
                  className={`w-full border ${publicNameError ? 'border-red-500' : 'border-gray-300'} rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all`}
                  value={publicName}
                  onChange={(e) => setPublicName(e.target.value)}
                />
                {publicNameError && (
                  <div className="flex items-center mt-1 text-red-500 text-sm">
                    <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                    {publicNameError}
                  </div>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Öffentlicher Name: {publicName.replace(/\s+/g, '-')}
                  {fileExtension}
                </p>
              </div>
            </div>

            {previewUrl && (
              <div className="space-y-3 pt-2 pb-1">
                <div className="flex items-center mb-2">
                  <input
                    type="checkbox"
                    id="enable-resize"
                    checked={enableResize}
                    onChange={(e) => setEnableResize(e.target.checked)}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 rounded"
                  />
                  <label htmlFor="enable-resize" className="text-sm font-medium text-gray-700">
                    Bildgröße ändern
                  </label>
                </div>

                <div className="flex justify-between text-sm text-gray-600">
                  <div>
                    <span className="font-medium">Original:</span> {imageNaturalSize.width} × {imageNaturalSize.height}{' '}
                    px
                  </div>
                  {enableResize && (
                    <div>
                      <span className="font-medium">Neue Größe:</span> {displayDimensions.width} ×{' '}
                      {displayDimensions.height} px
                    </div>
                  )}
                </div>

                {enableResize && (
                  <div className="flex items-center gap-3">
                    <div className="flex-1">
                      <label className="block text-xs text-gray-500 mb-1">Größe:</label>
                      <input
                        type="range"
                        min="32"
                        max={Math.min(400, Math.max(imageNaturalSize.width, imageNaturalSize.height))}
                        value={previewSize}
                        onChange={(e) => setPreviewSize(parseInt(e.target.value))}
                        className="w-full accent-blue-600"
                      />
                    </div>
                    <div className="w-20 flex items-center">
                      <input
                        type="number"
                        min="32"
                        max={Math.min(400, Math.max(imageNaturalSize.width, imageNaturalSize.height))}
                        value={previewSize}
                        onChange={handleSizeInputChange}
                        className="w-full border border-gray-300 rounded-lg px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="flex gap-3 pt-2">
              <button
                onClick={resetForm}
                type="button"
                className="flex-1 px-4 py-2 rounded-lg text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 transition-colors flex items-center justify-center"
              >
                <XMarkIcon className="h-5 w-5 mr-2" />
                Abbrechen
              </button>
              <button
                onClick={handleUpload}
                disabled={uploading || nameError !== null || publicNameError !== null || !selectedFile}
                className={`flex-1 px-4 py-2 rounded-lg text-white flex items-center justify-center transition-colors ${
                  uploading || nameError !== null || publicNameError !== null || !selectedFile
                    ? 'bg-blue-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {uploading ? (
                  <>
                    <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
                    Wird hochgeladen...
                  </>
                ) : (
                  <>
                    <ArrowUpTrayIcon className="h-5 w-5 mr-2" />
                    Icon hochladen
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Preview - right side */}
          {previewUrl && (
            <div className="flex-1">
              <div className="relative">
                <div
                  className={`flex items-center justify-center border border-gray-200 rounded-lg shadow-sm overflow-hidden transition-colors duration-300 ${
                    darkBackground ? 'bg-gray-800' : 'bg-white'
                  }`}
                  style={{ width: '400px', height: '400px' }}
                >
                  <img
                    src={previewUrl}
                    alt="Icon Vorschau"
                    style={{
                      width: `${displayDimensions.width}px`,
                      height: `${displayDimensions.height}px`,
                    }}
                    className="object-contain"
                    onLoad={handleImageLoad}
                  />
                </div>

                {/* Toggle switch in top-right corner */}
                <div className="absolute top-2 right-6">
                  <button
                    onClick={toggleBackground}
                    type="button"
                    className="p-1 rounded-full bg-white border border-gray-200 shadow-sm hover:bg-gray-50 transition-colors"
                    aria-label={darkBackground ? 'Heller Hintergrund' : 'Dunkler Hintergrund'}
                  >
                    {darkBackground ? (
                      <SunIcon className="h-5 w-5 text-orange-500" />
                    ) : (
                      <MoonIcon className="h-5 w-5 text-gray-700" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
