import { useCallback, useEffect, useState } from 'react';
import type { PublicImage } from '@prisma/client';

export type Icon = PublicImage & { url: string };

export function useIcons() {
  const [icons, setIcons] = useState<Icon[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Load existing icons
  const fetchIcons = useCallback(async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/admin/images?type=EMAIL_ICON');
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
      const data = await res.json();
      // Handle different response formats
      const iconData = Array.isArray(data) ? data : (data.data || []);
      setIcons(iconData);
      setError(null);
    } catch (_err) {
      setError('Icons konnten nicht geladen werden');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchIcons();
  }, [fetchIcons]);

  // Upload icon
  const uploadIcon = async (
    file: File,
    name: string,
    publicName: string,
    resize?: { width: number; height: number }
  ) => {
    setError(null);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', name);
    formData.append('publicName', publicName);
    formData.append('type', 'EMAIL_ICON');

    // Add resize parameters if provided
    if (resize) {
      formData.append('resize', JSON.stringify(resize));
    }

    try {
      const res = await fetch('/api/admin/images', {
        method: 'POST',
        body: formData,
      });

      if (!res.ok) {
        const txt = await res.text();
        throw new Error(txt);
      }

      const newIcon: Icon = await res.json();
      setIcons((prev) => [newIcon, ...prev]);
      return true;
    } catch (err) {
      setError(`Upload failed: ${err instanceof Error ? err.message : String(err)}`);
      return false;
    }
  };

  // Delete icon
  const deleteIcon = async (id: string): Promise<void> => {
    setError(null);

    try {
      const res = await fetch(`/api/admin/images/${id}`, {
        method: 'DELETE',
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || `HTTP error! status: ${res.status}`);
      }

      // Remove the deleted icon from the state
      setIcons((prev) => prev.filter((icon) => icon.id !== id));
    } catch (err) {
      setError(`Delete failed: ${err instanceof Error ? err.message : String(err)}`);
      throw err;
    }
  };

  return {
    icons,
    error,
    loading,
    uploadIcon,
    deleteIcon,
  };
}
