import { PublicImage, PublicImageType } from '@prisma/client';

export type ImageWithUrl = PublicImage & { url: string };

export interface ImageCategory {
  key: string;
  label: string;
  type: PublicImageType;
  prefix: string;
}

export const IMAGE_CATEGORIES: readonly ImageCategory[] = [
  {
    key: 'EMAIL_ICON',
    label: 'Icons',
    type: PublicImageType.EMAIL_ICON,
    prefix: 'email'
  },
  {
    key: 'BLOG_IMAGE',
    label: 'Blog-Bilder',
    type: PublicImageType.BLOG_IMAGE,
    prefix: 'blog'
  }
] as const;

export interface ImageUploadOptions {
  file: File;
  name: string;
  publicName: string;
  resize?: { width: number; height: number };
  crop?: { left: number; top: number; width: number; height: number };
}

export interface ImageSelectorProps {
  imageType: PublicImageType;
  onImageSelect?: (imageUrl: string) => void;
  layout?: 'grid-3' | 'grid-4';
  showCopyToClipboard?: boolean;
  title?: string;
  className?: string;
}

export interface ImageListProps {
  images: ImageWithUrl[];
  onDelete?: (imageId: string) => Promise<void>;
  layout?: 'grid-3' | 'grid-4';
  showCopyToClipboard?: boolean;
  showDeleteButton?: boolean;
}
