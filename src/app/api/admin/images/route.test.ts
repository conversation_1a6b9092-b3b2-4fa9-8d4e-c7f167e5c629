// src/app/api/admin/images/route.test.ts

/**
 * @jest-environment node
 */

import sharp from 'sharp';

// Test the core image processing logic separately from the API route
describe('Image Processing - Crop and Extend functionality', () => {
  // Helper function to create a test image buffer
  async function createTestImage(width: number = 200, height: number = 200): Promise<Buffer> {
    return await sharp({
      create: {
        width,
        height,
        channels: 4,
        background: { r: 255, g: 0, b: 0, alpha: 1 }
      }
    })
    .png()
    .toBuffer();
  }

  describe('Crop functionality', () => {
    it('should successfully crop an image', async () => {
      const imageBuffer = await createTestImage(200, 200);

      // Test cropping with <PERSON> directly
      const croppedBuffer = await sharp(imageBuffer)
        .extract({
          left: 50,
          top: 50,
          width: 100,
          height: 100
        })
        .toBuffer();

      const metadata = await sharp(croppedBuffer).metadata();
      expect(metadata.width).toBe(100);
      expect(metadata.height).toBe(100);
    });

    it('should handle crop parameters validation logic', () => {
      const originalWidth = 200;
      const originalHeight = 200;

      // Test valid crop parameters
      const validCrop = { left: 50, top: 50, width: 100, height: 100 };
      const isValidCrop = (
        typeof validCrop.left === 'number' &&
        typeof validCrop.top === 'number' &&
        typeof validCrop.width === 'number' &&
        typeof validCrop.height === 'number' &&
        validCrop.left >= 0 &&
        validCrop.top >= 0 &&
        validCrop.width > 0 &&
        validCrop.height > 0 &&
        validCrop.left + validCrop.width <= originalWidth &&
        validCrop.top + validCrop.height <= originalHeight
      );
      expect(isValidCrop).toBe(true);

      // Test invalid crop parameters (out of bounds)
      const invalidCrop = { left: 150, top: 150, width: 100, height: 100 };
      const isInvalidCrop = (
        invalidCrop.left + invalidCrop.width > originalWidth ||
        invalidCrop.top + invalidCrop.height > originalHeight
      );
      expect(isInvalidCrop).toBe(true);
    });
  });

  describe('Extend functionality', () => {
    it('should successfully extend an image', async () => {
      const imageBuffer = await createTestImage(100, 100);

      // Test extending with Sharp directly
      const extendedBuffer = await sharp(imageBuffer)
        .extend({
          top: 10,
          bottom: 10,
          left: 20,
          right: 20,
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .toBuffer();

      const metadata = await sharp(extendedBuffer).metadata();
      expect(metadata.width).toBe(140); // 100 + 20 + 20
      expect(metadata.height).toBe(120); // 100 + 10 + 10
    });

    it('should handle extend parameters validation logic', () => {
      // Test valid extend parameters
      const validExtend = { top: 10, bottom: 10, left: 20, right: 20 };
      const isValidExtend = (
        (validExtend.top === undefined || (typeof validExtend.top === 'number' && validExtend.top >= 0)) &&
        (validExtend.bottom === undefined || (typeof validExtend.bottom === 'number' && validExtend.bottom >= 0)) &&
        (validExtend.left === undefined || (typeof validExtend.left === 'number' && validExtend.left >= 0)) &&
        (validExtend.right === undefined || (typeof validExtend.right === 'number' && validExtend.right >= 0))
      );
      expect(isValidExtend).toBe(true);

      // Test invalid extend parameters (negative values)
      const invalidExtend = { top: -10, bottom: 10, left: 20, right: 20 };
      const isInvalidExtend = (
        (invalidExtend.top !== undefined && (typeof invalidExtend.top !== 'number' || invalidExtend.top < 0))
      );
      expect(isInvalidExtend).toBe(true);
    });
  });

  describe('Combined operations', () => {
    it('should successfully apply crop, resize, and extend in sequence', async () => {
      const imageBuffer = await createTestImage(200, 200);

      // Test the complete pipeline: crop -> resize -> extend
      const processedBuffer = await sharp(imageBuffer)
        // 1. Crop
        .extract({
          left: 25,
          top: 25,
          width: 150,
          height: 150
        })
        // 2. Resize
        .resize(100, 100, {
          fit: 'contain',
          background: { r: 0, g: 0, b: 0, alpha: 0 }
        })
        // 3. Extend
        .extend({
          top: 10,
          bottom: 10,
          left: 10,
          right: 10,
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .toBuffer();

      const metadata = await sharp(processedBuffer).metadata();
      expect(metadata.width).toBe(120); // 100 + 10 + 10
      expect(metadata.height).toBe(120); // 100 + 10 + 10
    });
  });

  describe('Parameter parsing and validation', () => {
    it('should correctly parse JSON parameters', () => {
      const cropParam = '{"left":50,"top":50,"width":100,"height":100}';
      const extendParam = '{"top":10,"bottom":10,"left":20,"right":20}';

      expect(() => JSON.parse(cropParam)).not.toThrow();
      expect(() => JSON.parse(extendParam)).not.toThrow();

      const cropOptions = JSON.parse(cropParam);
      const extendOptions = JSON.parse(extendParam);

      expect(cropOptions.left).toBe(50);
      expect(cropOptions.width).toBe(100);
      expect(extendOptions.top).toBe(10);
      expect(extendOptions.right).toBe(20);
    });

    it('should handle invalid JSON gracefully', () => {
      const invalidJson = '{"left":50,"top":50,invalid}';

      expect(() => JSON.parse(invalidJson)).toThrow();
    });
  });
});
