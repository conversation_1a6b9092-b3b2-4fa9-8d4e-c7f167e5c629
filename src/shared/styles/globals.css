@import 'tailwindcss';
@plugin "@tailwindcss/forms";

@tailwind utilities;

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

@theme {
  --breakpoint-xs: 370px;
  --color-primary: #fb8800;
}

:root {
  --side-padding: clamp(32px, calc(32px + 68 * ((100vw - 768px) / 732)), 100px);
  --hero-center-point: clamp(200px, 55%, 1000px);
  scrollbar-gutter: stable;
}

#test-safari-select {
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236B7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 0.5rem center !important;
  background-size: 1em !important;
  padding-right: 2.5rem !important;
  background-color: #ffffff !important;
  border-radius: 0.375rem !important;
  border: 1px solid #d1d5db !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;

  /* Override Safari's internal rendering */
  text-indent: 0.01px;
  text-overflow: '';
}

/* Remove Safari's focus ring and use our own */
#test-safari-select:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
  border-color: #10b981 !important;
}

.side-padding {
  padding-left: var(--side-padding);
  padding-right: var(--side-padding);
}

/* Marching ants effect for crop selection */
.marching-ants-border {
  border: 2px solid transparent;
  background:
    linear-gradient(0deg, #000 50%, #fff 50%) 0 0 / 8px 8px,
    linear-gradient(90deg, #000 50%, #fff 50%) 0 0 / 8px 8px,
    linear-gradient(180deg, #000 50%, #fff 50%) 0 0 / 8px 8px,
    linear-gradient(270deg, #000 50%, #fff 50%) 0 0 / 8px 8px;
  background-repeat: repeat-x, repeat-y, repeat-x, repeat-y;
  background-size: 8px 2px, 2px 8px, 8px 2px, 2px 8px;
  background-position: 0 0, 0 0, 0 100%, 100% 0;
  animation: marching-ants 0.5s linear infinite;
}

@keyframes marching-ants {
  0% {
    background-position: 0 0, 0 0, 0 100%, 100% 0;
  }
  100% {
    background-position: 8px 0, 0 8px, -8px 100%, 100% -8px;
  }
}

.side-padding-left {
  padding-left: var(--side-padding);
}

.side-padding-right {
  padding-right: var(--side-padding);
}

.custom-container {
  width: 100%;
  max-width: 1500px;
  margin-inline: auto;
  padding-left: var(--side-padding);
  padding-right: var(--side-padding);
}

/* Hide scrollbar but keep functionality */
@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-33.333% - 1.5rem));
  }
}

.animate-scroll {
  animation: scroll 3s linear infinite;
}

/* UserButton dropdown animations */
.dropdown-enter {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}

.dropdown-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition:
    opacity 300ms,
    transform 300ms;
}

.dropdown-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.dropdown-exit-active {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
  transition:
    opacity 200ms,
    transform 200ms;
}

/* Add this to your globals.css if not already present */
/* For upward animation (when dropdown opens above the button) */
.dropdown-reverse-enter {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}

.dropdown-reverse-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition:
    opacity 300ms,
    transform 300ms;
}

.dropdown-reverse-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.dropdown-reverse-exit-active {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
  transition:
    opacity 300ms,
    transform 300ms;
}

/*Modal opening animation*/
@keyframes modalIn {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.97);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-modalIn {
  animation: modalIn 0.25s ease-in-out forwards;
}

@keyframes overlayIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.animate-overlayIn {
  animation: overlayIn 0.35s ease-in-out forwards;
}

/* Expandable row animation */
@keyframes expandRow {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 2000px;
    opacity: 1;
  }
}

.animate-expandRow {
  animation: expandRow 0.3s ease-in-out forwards;
}

@keyframes collapseRow {
  from {
    max-height: 2000px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}

.animate-collapseRow {
  animation: collapseRow 0.3s ease-in-out forwards;
}

/* Add these closing animations after your existing modal animations */
@keyframes modalOut {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(10px) scale(0.97);
  }
}

.animate-modalOut {
  animation: modalOut 0.25s ease-in-out forwards;
}

@keyframes overlayOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.animate-overlayOut {
  animation: overlayOut 0.35s ease-in-out forwards;
}

/* Blog content styles - ensure consistency between editor and public view */
.blog-content {
  font-family: Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
}

.blog-content h1 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  margin-top: 2rem;
  color: #111827;
}

.blog-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  margin-top: 1.5rem;
  color: #111827;
}

.blog-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  margin-top: 1.25rem;
  color: #111827;
}

.blog-content p {
  color: #374151;
  margin-bottom: 1rem;
  line-height: 1.75;
}

.blog-content a {
  color: #2563eb;
  text-decoration: none;
}

.blog-content a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.blog-content strong {
  color: #111827;
  font-weight: 600;
}

.blog-content ul,
.blog-content ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.blog-content li {
  color: #374151;
  margin: 0.25rem 0;
}

.blog-content blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  font-style: italic;
  color: #6b7280;
  margin: 1rem 0;
}

.blog-content img {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

.blog-content code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.blog-content pre {
  background-color: #1f2937;
  color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
