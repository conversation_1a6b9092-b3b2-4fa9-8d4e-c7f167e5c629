# Image Processing API - Crop and Extend Functionality

The `/api/admin/images` endpoint now supports advanced image processing operations including cropping and extending images.

## Overview

The endpoint processes images in the following order:
1. **<PERSON>rop** - Extract a specific region from the image
2. **Resize** - Resize the image (existing functionality)
3. **Extend** - Add padding/borders to the image

All parameters are optional and backward compatible with existing implementations.

## Parameters

### Existing Parameters
- `file` (required) - The image file to upload
- `name` (required) - Internal name for the image
- `publicName` (required) - Public filename
- `type` (required) - Image type (EMAIL_ICON, BLOG_IMAGE, etc.)
- `resize` (optional) - JSON string with resize options

### New Parameters

#### `crop` (optional)
JSON string containing crop parameters:

```json
{
  "left": 50,     // X-offset from left edge (pixels)
  "top": 50,      // Y-offset from top edge (pixels)  
  "width": 100,   // Width of crop region (pixels)
  "height": 100   // Height of crop region (pixels)
}
```

**Validation:**
- All values must be positive numbers
- Crop region must be within original image bounds
- `left + width <= originalWidth`
- `top + height <= originalHeight`

#### `extend` (optional)
JSON string containing extend parameters:

```json
{
  "top": 10,                                           // Pixels to add to top
  "bottom": 10,                                        // Pixels to add to bottom
  "left": 20,                                          // Pixels to add to left
  "right": 20,                                         // Pixels to add to right
  "extendWith": "background",                          // How to fill new areas
  "background": {"r": 255, "g": 255, "b": 255, "alpha": 1}  // Background color
}
```

**Extend Methods:**
- `"background"` (default) - Fill with solid color
- `"copy"` - Copy edge pixels
- `"repeat"` - Repeat edge pixels
- `"mirror"` - Mirror edge pixels

**Default Values:**
- `extendWith`: `"background"`
- `background`: White `{"r": 255, "g": 255, "b": 255, "alpha": 1}`

## Usage Examples

### Basic Crop
```javascript
const formData = new FormData();
formData.append('file', imageFile);
formData.append('name', 'cropped-image');
formData.append('publicName', 'cropped-image.jpg');
formData.append('type', 'EMAIL_ICON');
formData.append('crop', JSON.stringify({
  left: 100,
  top: 50,
  width: 200,
  height: 200
}));
```

### Basic Extend (Add White Padding)
```javascript
const formData = new FormData();
formData.append('file', imageFile);
formData.append('name', 'padded-image');
formData.append('publicName', 'padded-image.jpg');
formData.append('type', 'BLOG_IMAGE');
formData.append('extend', JSON.stringify({
  top: 20,
  bottom: 20,
  left: 50,
  right: 50
}));
```

### Combined Operations
```javascript
const formData = new FormData();
formData.append('file', imageFile);
formData.append('name', 'processed-image');
formData.append('publicName', 'processed-image.jpg');
formData.append('type', 'EMAIL_ICON');

// 1. Crop to specific region
formData.append('crop', JSON.stringify({
  left: 25,
  top: 25,
  width: 150,
  height: 150
}));

// 2. Resize to target dimensions
formData.append('resize', JSON.stringify({
  width: 100,
  height: 100
}));

// 3. Add padding to achieve desired aspect ratio
formData.append('extend', JSON.stringify({
  top: 10,
  bottom: 10,
  left: 10,
  right: 10,
  background: {"r": 255, "g": 255, "b": 255, "alpha": 1}
}));
```

### Extend with Different Methods
```javascript
// Mirror edges for artistic effect
formData.append('extend', JSON.stringify({
  top: 20,
  bottom: 20,
  left: 20,
  right: 20,
  extendWith: "mirror"
}));

// Repeat edge pixels
formData.append('extend', JSON.stringify({
  right: 50,
  extendWith: "repeat"
}));
```

## Common Use Cases

### 1. Crop then Resize
Extract a specific region and resize to target dimensions:
- Crop: Extract the important part of the image
- Resize: Scale to desired size

### 2. Resize then Extend
Resize image and add padding to achieve exact aspect ratio:
- Resize: Scale image to fit within target dimensions
- Extend: Add white padding to reach exact dimensions

### 3. Change Aspect Ratio Without Losing Content
Add padding to change aspect ratio without cropping:
- Extend: Add white space to sides/top/bottom as needed

## Error Handling

The API returns appropriate error messages for invalid parameters:

- `400` - Invalid crop parameters (out of bounds, negative values)
- `400` - Invalid extend parameters (negative values)
- `400` - Invalid JSON format for crop/extend parameters
- `400` - Missing required fields
- `401` - Unauthorized (admin access required)

## Backward Compatibility

All new parameters are optional. Existing code will continue to work without modification:

```javascript
// This still works exactly as before
const formData = new FormData();
formData.append('file', imageFile);
formData.append('name', 'image');
formData.append('publicName', 'image.jpg');
formData.append('type', 'EMAIL_ICON');
formData.append('resize', JSON.stringify({width: 100, height: 100}));
```

## Technical Details

- Processing order: Crop → Resize → Extend
- Uses Sharp.js for high-performance image processing
- Maintains image quality and supports all common formats
- Automatically updates width/height metadata in database
- Preserves original MIME type
